# Project Chronos Environment Configuration
# Copy this file to .env and update values for your environment

# Application Settings
CHRONOS_PROJECT_NAME="Project Chronos"
CHRONOS_VERSION="0.1.0"
CHRONOS_SECRET_KEY="your-secret-key-here-change-in-production"
CHRONOS_SERVER_HOST="http://localhost:8000"

# Database Configuration
CHRONOS_POSTGRES_SERVER="localhost"
CHRONOS_POSTGRES_USER="chronos"
CHRONOS_POSTGRES_PASSWORD="chronos_password"
CHRONOS_POSTGRES_DB="chronos"
CHRONOS_POSTGRES_PORT=5432

# Test Database Configuration
CHRONOS_TEST_DATABASE_URL="postgresql+asyncpg://chronos:chronos_password@localhost:5433/chronos_test"

# Redis Configuration
CHRONOS_REDIS_HOST="localhost"
CHRONOS_REDIS_PORT=6379
CHRONOS_REDIS_DB=0
CHRONOS_REDIS_PASSWORD=""

# Security Settings
CHRONOS_ACCESS_TOKEN_EXPIRE_MINUTES=15
CHRONOS_REFRESH_TOKEN_EXPIRE_DAYS=7
CHRONOS_JWT_ALGORITHM="HS256"

# AI Integration (Optional)
CHRONOS_OPENAI_API_KEY=""
CHRONOS_ANTHROPIC_API_KEY=""
CHRONOS_AI_CHUNKING_ENABLED=true
CHRONOS_AI_CHUNKING_CACHE_TTL=3600

# Email Configuration (Optional)
CHRONOS_SMTP_TLS=true
CHRONOS_SMTP_PORT=587
CHRONOS_SMTP_HOST=""
CHRONOS_SMTP_USER=""
CHRONOS_SMTP_PASSWORD=""
CHRONOS_EMAILS_FROM_EMAIL=""
CHRONOS_EMAILS_FROM_NAME="Project Chronos"

# ADHD-Specific Settings
CHRONOS_DEFAULT_CHUNK_SIZE="small"
CHRONOS_MAX_SUBTASKS_PER_CHUNK=7
CHRONOS_DEFAULT_FOCUS_DURATION=25
CHRONOS_DEFAULT_BREAK_DURATION=5
CHRONOS_HYPERFOCUS_WARNING_THRESHOLD=120

# Rate Limiting
CHRONOS_RATE_LIMIT_PER_MINUTE=100
CHRONOS_RATE_LIMIT_PER_HOUR=1000

# Logging
CHRONOS_LOG_LEVEL="INFO"

# Development Settings
CHRONOS_TESTING=false
