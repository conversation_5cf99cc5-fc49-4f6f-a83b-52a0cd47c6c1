"""
Unit tests for User model.

Tests ADHD-specific user functionality including preferences,
energy patterns, and accessibility features.
"""

import pytest
from chronos.app.models.user import User


class TestUserModel:
    """Test User model functionality."""
    
    def test_user_creation(self):
        """Test basic user creation."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            full_name="Test User"
        )
        
        assert user.email == "<EMAIL>"
        assert user.hashed_password == "hashed_password"
        assert user.full_name == "Test User"
        assert user.is_active is True
        assert user.is_verified is False
        assert user.adhd_diagnosed is False
        assert user.timezone == "UTC"
        assert user.preferences == {}
    
    def test_adhd_user_creation(self):
        """Test ADHD user creation with specific fields."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            adhd_diagnosed=True,
            preferences={
                "energy_patterns": {"morning": "high", "afternoon": "low"},
                "notification_preferences": {"persistent": True}
            }
        )
        
        assert user.adhd_diagnosed is True
        assert user.is_adhd_user() is True
        assert user.get_preference("energy_patterns.morning") == "high"
        assert user.get_preference("notification_preferences.persistent") is True
    
    def test_preference_management(self):
        """Test preference getting and setting."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        # Test setting nested preference
        user.set_preference("energy_patterns.morning", "high")
        assert user.get_preference("energy_patterns.morning") == "high"
        
        # Test setting top-level preference
        user.set_preference("theme", "dark")
        assert user.get_preference("theme") == "dark"
        
        # Test default value
        assert user.get_preference("nonexistent", "default") == "default"
    
    def test_energy_patterns(self):
        """Test energy pattern management."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        # Test setting energy pattern
        user.set_energy_pattern("morning", "high")
        assert user.get_energy_pattern("morning") == "high"
        
        # Test default energy pattern
        assert user.get_energy_pattern("evening") == "medium"
    
    def test_notification_preferences(self):
        """Test ADHD-friendly notification preferences."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        prefs = user.get_notification_preferences()
        
        # Check ADHD-friendly defaults
        assert prefs["persistent"] is True
        assert prefs["gentle"] is True
        assert prefs["staggered"] is True
        assert prefs["focus_mode_respect"] is True
        assert prefs["max_per_hour"] == 5
    
    def test_chunking_preferences(self):
        """Test AI chunking preferences."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        prefs = user.get_chunking_preferences()
        
        # Check ADHD-friendly defaults
        assert prefs["default_size"] == "small"
        assert prefs["max_subtasks"] == 5
        assert prefs["include_time_estimates"] is True
        assert prefs["include_energy_levels"] is True
    
    def test_focus_preferences(self):
        """Test focus session preferences."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        prefs = user.get_focus_preferences()
        
        # Check ADHD-friendly defaults
        assert prefs["default_duration"] == 25
        assert prefs["break_duration"] == 5
        assert prefs["hyperfocus_warning"] == 120
        assert prefs["gentle_transitions"] is True
    
    def test_accessibility_preferences(self):
        """Test accessibility preferences."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        prefs = user.get_accessibility_preferences()
        
        # Check ADHD-helpful defaults
        assert prefs["color_coding"] is True
        assert prefs["visual_timers"] is True
        assert prefs["progress_indicators"] is True
    
    def test_is_adhd_user(self):
        """Test ADHD user detection."""
        # User with diagnosis
        adhd_user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            adhd_diagnosed=True
        )
        assert adhd_user.is_adhd_user() is True
        
        # User with ADHD support mode
        support_user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            preferences={"adhd_support_mode": True}
        )
        assert support_user.is_adhd_user() is True
        
        # Regular user
        regular_user = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        assert regular_user.is_adhd_user() is False
    
    def test_user_representation(self):
        """Test user string representation."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            adhd_diagnosed=True
        )
        
        repr_str = repr(user)
        assert "<EMAIL>" in repr_str
        assert "adhd_diagnosed=True" in repr_str
