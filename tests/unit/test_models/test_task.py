"""
Unit tests for Task model.

Tests ADHD-specific task functionality including energy levels,
context tags, and hierarchical relationships for chunking.
"""

import pytest
from datetime import datetime, timedelta
from chronos.app.models.task import Task


class TestTaskModel:
    """Test Task model functionality."""
    
    def test_task_creation(self):
        """Test basic task creation."""
        task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Test Task",
            description="A test task"
        )
        
        assert task.title == "Test Task"
        assert task.description == "A test task"
        assert task.status == "pending"
        assert task.priority == "medium"
        assert task.energy_level == "medium"
        assert task.context_tags == []
        assert task.is_chunked is False
        assert task.parent_task_id is None
    
    def test_adhd_specific_fields(self):
        """Test ADHD-specific task fields."""
        task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="ADHD Task",
            energy_level="low",
            estimated_duration=15,
            context_tags=["home", "quick"]
        )
        
        assert task.energy_level == "low"
        assert task.estimated_duration == 15
        assert "home" in task.context_tags
        assert "quick" in task.context_tags
    
    def test_is_overdue(self):
        """Test overdue detection."""
        # Task with future due date
        future_task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Future Task",
            due_date=datetime.utcnow() + timedelta(hours=1)
        )
        assert future_task.is_overdue() is False
        
        # Task with past due date
        past_task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Past Task",
            due_date=datetime.utcnow() - timedelta(hours=1)
        )
        assert past_task.is_overdue() is True
        
        # Completed task (not overdue even if past due date)
        completed_task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Completed Task",
            status="completed",
            due_date=datetime.utcnow() - timedelta(hours=1)
        )
        assert completed_task.is_overdue() is False
    
    def test_subtask_relationships(self):
        """Test parent-child task relationships."""
        parent_task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Parent Task",
            is_chunked=True
        )
        
        subtask = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Subtask",
            parent_task_id=parent_task.id
        )
        
        assert parent_task.has_subtasks() is False  # No subtasks added yet
        assert subtask.is_subtask() is True
        assert parent_task.is_subtask() is False
    
    def test_context_tag_management(self):
        """Test context tag operations."""
        task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Test Task"
        )
        
        # Add context tags
        task.add_context_tag("home")
        task.add_context_tag("computer")
        assert "home" in task.context_tags
        assert "computer" in task.context_tags
        
        # Don't add duplicates
        task.add_context_tag("home")
        assert task.context_tags.count("home") == 1
        
        # Remove context tag
        task.remove_context_tag("home")
        assert "home" not in task.context_tags
        assert "computer" in task.context_tags
    
    def test_context_matching(self):
        """Test context tag matching."""
        task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Test Task",
            context_tags=["home", "computer", "quiet"]
        )
        
        # Should match if all required tags are present
        assert task.matches_context(["home"]) is True
        assert task.matches_context(["home", "computer"]) is True
        
        # Should not match if any required tag is missing
        assert task.matches_context(["home", "office"]) is False
        assert task.matches_context(["office"]) is False
        
        # Empty requirements should match
        assert task.matches_context([]) is True
    
    def test_energy_level_suitability(self):
        """Test energy level matching."""
        low_energy_task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Low Energy Task",
            energy_level="low"
        )
        
        high_energy_task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="High Energy Task",
            energy_level="high"
        )
        
        # Low energy task suitable for all energy levels
        assert low_energy_task.is_suitable_for_energy_level("low") is True
        assert low_energy_task.is_suitable_for_energy_level("medium") is True
        assert low_energy_task.is_suitable_for_energy_level("high") is True
        
        # High energy task only suitable for high energy
        assert high_energy_task.is_suitable_for_energy_level("low") is False
        assert high_energy_task.is_suitable_for_energy_level("medium") is False
        assert high_energy_task.is_suitable_for_energy_level("high") is True
    
    def test_urgency_score(self):
        """Test urgency score calculation."""
        # Base priority scores
        low_priority_task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Low Priority",
            priority="low"
        )
        assert low_priority_task.get_urgency_score() == 1
        
        urgent_task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Urgent Task",
            priority="urgent"
        )
        assert urgent_task.get_urgency_score() == 4
        
        # Due date affects urgency
        overdue_task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Overdue Task",
            priority="medium",
            due_date=datetime.utcnow() - timedelta(hours=1)
        )
        assert overdue_task.get_urgency_score() == 7  # 2 + 5 for overdue
        
        due_today_task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Due Today",
            priority="medium",
            due_date=datetime.utcnow() + timedelta(hours=2)
        )
        assert due_today_task.get_urgency_score() == 5  # 2 + 3 for due today
    
    def test_task_representation(self):
        """Test task string representation."""
        task = Task(
            user_id="550e8400-e29b-41d4-a716-446655440000",
            title="Test Task",
            status="pending",
            energy_level="medium"
        )
        
        repr_str = repr(task)
        assert "Test Task" in repr_str
        assert "pending" in repr_str
        assert "medium" in repr_str
