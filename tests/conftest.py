"""
Test configuration and fixtures for Project Chronos.

This module provides shared test fixtures and configuration for
comprehensive testing of ADHD-focused features.
"""

import asyncio
import pytest
from typing import AsyncGenerator, Generator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from chronos.app.core.database import Base
from chronos.app.core.config import settings

# Test database URL
TEST_DATABASE_URL = "postgresql+asyncpg://chronos:chronos_password@localhost:5432/chronos_test"


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create event loop for async tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_engine():
    """Create test database engine."""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Cleanup
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    await engine.dispose()


@pytest.fixture
async def db_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create database session for tests."""
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()


@pytest.fixture
async def adhd_user(db_session: AsyncSession):
    """Create test user with ADHD diagnosis."""
    from chronos.app.models.user import User
    
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="ADHD Test User",
        adhd_diagnosed=True,
        preferences={
            "energy_patterns": {
                "morning": "high",
                "afternoon": "medium", 
                "evening": "low"
            },
            "notification_preferences": {
                "persistent": True,
                "gentle": True
            },
            "chunking_preferences": {
                "default_size": "small",
                "max_subtasks": 5
            }
        }
    )
    
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    return user


@pytest.fixture
async def sample_task(db_session: AsyncSession, adhd_user):
    """Create sample task for testing."""
    from chronos.app.models.task import Task
    
    task = Task(
        user_id=adhd_user.id,
        title="Test Task",
        description="A test task for ADHD users",
        energy_level="medium",
        estimated_duration=30,
        context_tags=["home", "computer"]
    )
    
    db_session.add(task)
    await db_session.commit()
    await db_session.refresh(task)
    
    return task
